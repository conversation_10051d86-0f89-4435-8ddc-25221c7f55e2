image: registry.gapo.com.vn/android-work/android-ci-base-image:flutter-sdk

before_script:
  - export GRADLE_USER_HOME=/.gradle
  - export CACHE_PATH="/.gradle"
  - mv -v /root/.ssh.origin/* ~/.ssh
  - git config --global user.email "<EMAIL>"
  - git config --global user.name "flutter_ci_job"
    #  - if [ -d $CACHE_PATH/wrapper ]; then cp -r $CACHE_PATH/wrapper /.gradle/wrapper; fi
    #  - if [ -d $CACHE_PATH/caches ]; then cp -r $CACHE_PATH/caches /.gradle/caches; fi

stages:
  - build
  - deploy

.build_template:
  stage: build
  script:
    #- flutter clean
    - cd app/
    - flutter pub get
    # flutter pub run build_runner build --delete-conflicting-outputs
    - cd ../
    - sh script/prebuild/android-pre-build.sh
    - cd app/
    - flutter build aar --no-profile
    - cd /root/aar-center
    - git fetch origin
    - git checkout develop
    - git pull origin develop --ff
    - cp -r /builds/flutter/gapo_flutter/app/build/host/outputs/repo/* .
    - git add .
    - git commit -m "FLUTTER AAR production"
    - git push origin develop

flutter prod:
  extends: .build_template
  stage: deploy
  only:
    - release
  tags:
    - android
